###
Description:    自动翻译i18n表中的未翻译数据
                支持中文繁简体自动转换和韩文LLM翻译
                
Usage:          ./start.sh -n auto_translate_i18n -cmd "lib/batchBase.coffee batch/i18n/auto_translate_i18n.coffee dryrun forceKr --lang zh-cn,kr"
                
Parameters:
  --dryrun              干运行模式，只显示将要翻译的记录而不实际执行翻译操作
  --lang <languages>    指定要翻译的目标语言，支持单个语言（如 --lang zh）或多个语言（如 --lang zh,kr）
                        支持的语言: zh（中文繁体）, zh-cn（中文简体）, kr（韩文）
  --forceKr             强制翻译所有记录的韩文版本，包括已有翻译的记录（覆盖现有翻译）

Translation Strategy:
  - 中文繁体和简体：只需翻译其中一种（优先翻译zh-cn），另一种通过JTFTAUTO函数进行自动转换
  - 韩文：使用LLM翻译模型进行翻译，需要从prompts表获取韩文翻译的专用提示词模板

Create date:    2025-07-01
Author:         Luo xiaowei
Run frequency:  On demand
###

debug = DEBUG()
yargs = require 'yargs'
speed = INCLUDE 'lib.speed'
helpers = INCLUDE 'lib.helpers'
i18n = INCLUDE 'lib.i18n'
translatorManagerLib = INCLUDE 'lib.translator/translatorManager'

# 数据库集合
I18nCol = COLLECTION 'chome', 'i18n'
PromptsCol = COLLECTION 'chome', 'prompts'

# 配置和服务
config = CONFIG(['azure','i18n','deepseek','deepL','openAI','gemini','claude'])
translatorManager = translatorManagerLib.createTranslatorManager(config)

# 速度监控器
speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 10,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

# 命令行参数解析
yargs
  .option 'lang', {
    type: 'string'
    description: '指定要翻译的目标语言，支持多个语言用逗号分隔 (zh,zh-cn,kr)'
    default: 'zh-cn'
  }
  .help('help')

options = yargs.argv

# 全局变量
dryRun = AVGS.indexOf('dryrun') >= 0
startTs = new Date()
targetLanguages = []
forceKrAll =  AVGS.indexOf('forceKr') >= 0

# 支持的语言列表
SUPPORTED_LANGUAGES = ['zh', 'zh-cn', 'kr']
CHINESE_LANGUAGES = ['zh', 'zh-cn']

# 韩文翻译提示词缓存
koreanPromptCache = null

###
解析目标语言参数
@param {String} langParam - 语言参数字符串，如 'zh,kr' 或 'zh-cn'
@return {Array} 解析后的语言数组
###
parseTargetLanguages = (langParam) ->
  unless langParam
    return ['zh-cn']
  
  languages = langParam.split(',').map((lang) -> lang.trim()).filter((lang) -> lang.length > 0)
  validLanguages = []
  
  for lang in languages
    if lang in SUPPORTED_LANGUAGES
      validLanguages.push(lang)
    else
      debug.warn '不支持的语言: ' + lang + '，支持的语言: ' + SUPPORTED_LANGUAGES.join(', ')

  if validLanguages.length is 0
    debug.warn '没有有效的目标语言，使用默认语言: zh-cn'
    return ['zh-cn']
  
  return validLanguages

###
获取韩文翻译的提示词模板
@return {String} 韩文翻译提示词，如果获取失败则返回默认提示词
###
getKoreanTranslationPrompt = ->
  if koreanPromptCache
    return koreanPromptCache
  
  try
    # 查找韩文翻译的活跃模板
    prompts = await PromptsCol.findToArray {
      scenario: 'ui_translation'
      status: 'active'
      tags: { $in: ['korean', 'kr'] }
    }, {
      sort: { ver: -1, _mt: -1 }
      limit: 1
    }
    
    if prompts and prompts.length > 0
      prompt = prompts[0]
      if prompt.tpl?.main
        koreanPromptCache = prompt.tpl.main
        debug.info "成功获取韩文翻译提示词模板: #{prompt.nm}"
        return koreanPromptCache
    
    # 如果没有找到专用模板，使用默认韩文翻译提示词
    koreanPromptCache = '请将以下英文内容翻译为韩文，保持原意不变，只返回翻译结果：'
    debug.warn '未找到韩文翻译专用模板，使用默认提示词'
    return koreanPromptCache

  catch error
    debug.error '获取韩文翻译提示词时出错:', error
    koreanPromptCache = '请将以下英文内容翻译为韩文，保持原意不变，只返回翻译结果：'
    return koreanPromptCache

###
使用JTFTAUTO函数进行中文繁简转换
@param {String} text - 要转换的文本
@param {String} targetLang - 目标语言 ('zh' 或 'zh-cn')
@return {String} 转换后的文本
###
convertChineseText = (text, targetLang) ->
  unless text
    return text
  
  try
    return i18n.JTFTAUTO(text, targetLang)
  catch error
    debug.error '中文繁简转换失败:', error
    return text

###
构建查询条件以查找未翻译的记录
@param {Array} languages - 目标语言数组
@param {Boolean} forceKrAll - 是否强制重新翻译所有韩文记录
@return {Object} MongoDB查询条件
###
buildUntranslatedQuery = (languages, forceKrAll) ->
  query = { }
  
  # 构建语言过滤条件
  languageConditions = []
  
  for lang in languages
    if lang is 'kr' and forceKrAll
      # 强制翻译所有韩文记录，不检查是否已存在
      continue
    else
      # 查找该语言字段不存在或为空的记录
      condition = {}
      condition[lang] = { $exists: false }
      languageConditions.push(condition)
      
      # 同时查找该语言字段为空字符串的记录
      emptyCondition = {}
      emptyCondition[lang] = { $in: [null, ''] }
      languageConditions.push(emptyCondition)
  
  if languageConditions.length > 0
    query.$or = languageConditions
  
  return query

# 解析目标语言
targetLanguages = parseTargetLanguages(options.lang)
debug.info "目标语言: #{targetLanguages.join(', ')}"
debug.info "干运行模式: #{dryRun}"
debug.info "强制翻译韩文: #{forceKrAll}"

###
翻译单个记录的指定语言
@param {Object} record - i18n记录对象
@param {String} targetLang - 目标语言
@return {Object} 翻译结果 {success: boolean, translatedText?: string, error?: string}
###
translateRecord = (record, targetLang) ->
  try
    originalText = record.orig or record._id
    unless originalText
      return {success: false, error: '原文内容为空'}

    # 中文翻译处理
    if targetLang in CHINESE_LANGUAGES
      # 检查是否已有其他中文版本可以转换
      if targetLang is 'zh' and record['zh-cn']
        # 从简体转换为繁体
        translatedText = convertChineseText(record['zh-cn'], 'zh')
        return {success: true, translatedText: translatedText, method: 'convert_from_zhcn'}
      else if targetLang is 'zh-cn' and record['zh']
        # 从繁体转换为简体
        translatedText = convertChineseText(record['zh'], 'zh-cn')
        return {success: true, translatedText: translatedText, method: 'convert_from_zh'}
      else
        # 需要LLM翻译中文
        prompt = '请将以下英文内容翻译为中文，保持原意不变，只返回翻译结果：'
        result = await translatorManager.translateWithPrompt(
          originalText,
          prompt,
          targetLang,
          { skipI18nFilter: true }  # i18n内容跳过过滤
        )

        if result.success
          return {
            success: true
            translatedText: result.result
            method: 'llm_translate'
            service: result.service
          }
        else
          return {success: false, error: result.error}

    # 韩文翻译处理
    else if targetLang is 'kr'
      koreanPrompt = await getKoreanTranslationPrompt()
      result = await translatorManager.translateWithPrompt(
        originalText,
        koreanPrompt,
        'kr',
        { skipI18nFilter: true }  # i18n内容跳过过滤
      )

      if result.success
        return {
          success: true
          translatedText: result.result
          method: 'llm_translate'
          service: result.service
        }
      else
        return {success: false, error: result.error}

    else
      return {success: false, error: '不支持的目标语言: ' + targetLang}

  catch error
    debug.error '翻译记录时出错:', error
    return {success: false, error: error.message}

###
重试翻译操作
@param {Function} translateFn - 翻译函数
@param {Number} maxRetries - 最大重试次数
@param {Number} retryDelay - 重试延迟（毫秒）
@return {Object} 翻译结果
###
retryTranslation = (translateFn, maxRetries = 3, retryDelay = 1000) ->
  for attempt in [1..maxRetries]
    try
      result = await translateFn()
      if result.success
        return result
      else if attempt < maxRetries
        debug.warn "翻译失败，第 #{attempt} 次重试: #{result.error}"
        await new Promise((resolve) -> setTimeout(resolve, retryDelay * attempt))
      else
        return result
    catch error
      if attempt < maxRetries
        debug.warn "翻译异常，第 #{attempt} 次重试: #{error.message}"
        await new Promise((resolve) -> setTimeout(resolve, retryDelay * attempt))
      else
        return {success: false, error: error.message}

  return {success: false, error: '重试次数已用完'}

###
批量更新数据库记录
@param {Array} updates - 更新操作数组
@return {Object} 更新结果
###
batchUpdateRecords = (updates) ->
  unless updates and updates.length > 0
    return {success: true, modifiedCount: 0, errors: []}

  try
    operations = updates.map (update) ->
      {
        updateOne: {
          filter: { _id: update._id }
          update: update.updateData
        }
      }

    result = await I18nCol.bulkWrite operations, { ordered: false }

    return {
      success: true
      modifiedCount: result.modifiedCount
      upsertedCount: result.upsertedCount
      errors: []
    }

  catch error
    debug.error '批量更新数据库时出错:', error
    return {
      success: false
      modifiedCount: 0
      errors: [error.message]
    }

###
验证翻译结果质量
@param {String} originalText - 原文
@param {String} translatedText - 译文
@param {String} targetLang - 目标语言
@return {Object} 验证结果 {valid: boolean, issues: Array}
###
validateTranslation = (originalText, translatedText, targetLang) ->
  issues = []

  # 基本检查
  unless translatedText
    issues.push '翻译结果为空'
    return {valid: false, issues}

  # 长度检查（译文不应该比原文长太多）
  if translatedText.length > originalText.length * 3
    issues.push '译文长度异常（超过原文3倍）'

  # 检查是否包含明显的错误标识
  errorPatterns = [
    /error/i,
    /failed/i,
    /无法翻译/,
    /translation failed/i,
    /sorry/i
  ]

  for pattern in errorPatterns
    if pattern.test(translatedText)
      issues.push "译文包含错误标识: #{pattern}"

  # 语言特定检查
  if targetLang is 'kr'
    # 韩文应该包含韩文字符
    unless /[\u3131-\u3163\uac00-\ud7a3]/u.test(translatedText)
      issues.push '韩文翻译结果不包含韩文字符'
  else if targetLang in ['zh', 'zh-cn']
    # 中文应该包含中文字符
    unless /[\u4e00-\u9fff]/u.test(translatedText)
      issues.push '中文翻译结果不包含中文字符'

  return {
    valid: issues.length is 0
    issues: issues
  }

###
生成处理报告
@param {Object} stats - 统计数据
###
generateReport = (stats) ->
  report = []
  report.push '=== i18n自动翻译处理报告 ==='
  report.push "处理时间: #{new Date().toISOString()}"
  report.push "目标语言: #{targetLanguages.join(', ')}"
  report.push "处理模式: #{if dryRun then '干运行' else '实际执行'}"
  report.push ''

  report.push '=== 处理统计 ==='
  report.push "总处理记录: #{stats.processed or 0}"
  report.push "成功翻译: #{stats.translated or 0}"
  report.push "跳过记录: #{stats.skipped or 0}"
  report.push "翻译错误: #{stats.translationError or 0}"
  report.push "更新错误: #{stats.updateError or 0}"
  report.push "数据库更新: #{stats.updated or 0}"

  if stats.translated > 0
    totalAttempts = stats.translated + (stats.translationError or 0)
    successRate = ((stats.translated / totalAttempts) * 100).toFixed(2)
    report.push "翻译成功率: #{successRate}%"

  report.push ''
  report.push '=== 语言分布 ==='
  for lang in targetLanguages
    count = stats["#{lang}_translated"] or 0
    report.push "#{lang}: #{count} 条记录"

  if stats.qualityIssues and stats.qualityIssues.length > 0
    report.push ''
    report.push '=== 质量问题 ==='
    for issue in stats.qualityIssues.slice(0, 10)  # 只显示前10个问题
      report.push "- #{issue}"

  report.push ''
  report.push '=== 建议 ==='
  if (stats.translationError or 0) > (stats.translated or 0) * 0.1
    report.push '- 翻译错误率较高，建议检查网络连接和API配置'
  if (stats.updateError or 0) > 0
    report.push '- 存在数据库更新错误，建议检查数据库连接和权限'
  if stats.translated > 0
    report.push '- 建议对翻译结果进行人工抽查以确保质量'

  debug.info report.join('\n')

###
主处理函数
###
main = ->
  # 构建查询条件
  query = buildUntranslatedQuery(targetLanguages, forceKrAll)
  debug.info '查询条件:', JSON.stringify(query)

  # 构建投影，只获取需要的字段
  projection = {
    _id: 1,
    orig: 1
  }

  # 添加目标语言字段到投影中
  for lang in targetLanguages
    projection[lang] = 1

  # 添加中文字段以支持繁简转换
  if 'zh' in targetLanguages or 'zh-cn' in targetLanguages
    projection['zh'] = 1
    projection['zh-cn'] = 1

  # 执行查询
  cur = await I18nCol.find query, {projection: projection}
  cur.maxTimeMS 3600000  # 设置查询超时时间为1小时
  stream = cur.stream()

  # 使用helpers.streaming进行流式处理
  obj =
    verbose: 1,
    stream: stream,
    high: 10,  # 控制并发数
    end: (err) ->
      processTs = (new Date().getTime() - startTs.getTime()) / (1000 * 60)
      if err
        debug.error "处理过程中出错，总处理时间 #{processTs} 分钟。", speedMeter.toString()
        return EXIT 1, err

      # 生成处理报告
      stats = speedMeter.getCounters()
      stats.processTime = processTs
      generateReport(stats)

      debug.info "处理完成，总处理时间 #{processTs} 分钟，#{speedMeter.toString()}"
      EXIT 0
    process: (record, callback) ->
      speedMeter.check { processed: 1 }

      # 准备更新数据
      updateData = {
        noModifyMt: true,
        $set: {}
      }

      translationResults = []
      hasUpdates = false

      # 处理每个目标语言
      for targetLang in targetLanguages
        # 检查是否需要翻译该语言
        shouldTranslate = false

        if targetLang is 'kr' and forceKrAll
          # 强制翻译韩文
          shouldTranslate = true
        else if (not record[targetLang]) or (record[targetLang] is '')
          # 该语言字段不存在或为空
          shouldTranslate = true

        if shouldTranslate
          if dryRun
            debug.info "DryRun: 将翻译记录 #{record._id} 到 #{targetLang}"
            speedMeter.check { dryRun: 1 }
          else
            # 执行翻译（带重试机制）
            translationResult = await retryTranslation ->
              translateRecord(record, targetLang)

            translationResults.push({
              language: targetLang,
              result: translationResult
            })

            if translationResult.success
              # 验证翻译质量
              validation = validateTranslation(
                record.orig or record._id,
                translationResult.translatedText,
                targetLang
              )

              if validation.valid
                updateData.$set[targetLang] = translationResult.translatedText
                hasUpdates = true
                speedMeter.check { translated: 1 }
                speedMeter.check { "#{targetLang}_translated": 1 }
                debug.debug "成功翻译 #{record._id} -> #{targetLang}: #{translationResult.translatedText}"
              else
                speedMeter.check { qualityIssue: 1 }
                debug.warn "翻译质量问题 #{record._id} -> #{targetLang}: #{validation.issues.join(', ')}"
                # 仍然保存翻译结果，但记录质量问题
                updateData.$set[targetLang] = translationResult.translatedText
                hasUpdates = true
                speedMeter.check { translated: 1 }
                speedMeter.check { "#{targetLang}_translated": 1 }
            else
              speedMeter.check { translationError: 1 }
              debug.error "翻译失败 #{record._id} -> #{targetLang}: #{translationResult.error}"
        else
          speedMeter.check { skipped: 1 }
          debug.debug "跳过已翻译的记录 #{record._id} -> #{targetLang}"

      # 如果没有更新或干运行模式，不更新数据库
      if dryRun or (not hasUpdates)
        return callback()

      try
        await I18nCol.updateOne { _id: record._id }, updateData
        speedMeter.check { updated: 1 }
        debug.debug "成功更新数据库记录: #{record._id}"
      catch updateError
        speedMeter.check { updateError: 1 }
        debug.error "更新数据库失败 #{record._id}:", updateError

      return callback()

  helpers.streaming obj

# 启动主函数
main()
