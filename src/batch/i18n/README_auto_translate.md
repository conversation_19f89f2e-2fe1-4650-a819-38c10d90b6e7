# i18n自动翻译批处理脚本

## 概述

`auto_translate_i18n.coffee` 是一个用于自动翻译i18n表中未翻译数据的批处理脚本。支持中文繁简体自动转换和韩文LLM翻译。

## 功能特性

- **多语言支持**：支持中文繁体(zh)、中文简体(zh-cn)、韩文(kr)
- **智能翻译策略**：
  - 中文繁简体：优先翻译一种，另一种通过JTFTAUTO函数自动转换
  - 韩文：使用LLM翻译模型，从prompts表获取专用提示词
- **批量处理**：支持大量数据的高效处理
- **错误处理**：完整的重试机制和错误恢复
- **质量验证**：翻译结果质量检查和验证
- **进度监控**：实时进度显示和统计信息
- **干运行模式**：安全的预览模式

## 使用方法

### 基本用法

```bash
# 干运行模式（推荐首次使用）
./start.sh -n auto_translate_i18n -cmd "lib/batchBase.coffee batch/i18n/auto_translate_i18n.coffee --dryrun --lang zh-cn --limit 10"

# 实际执行翻译
./start.sh -n auto_translate_i18n -cmd "lib/batchBase.coffee batch/i18n/auto_translate_i18n.coffee --lang zh-cn --limit 100"
```

### 命令行参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--dryrun` | boolean | false | 干运行模式，只显示将要翻译的记录 |
| `--lang` | string | 'zh-cn' | 目标语言，支持多个语言用逗号分隔 |
| `--force-kr-all` | boolean | false | 强制翻译所有韩文记录（覆盖现有翻译） |
| `--limit` | number | 100 | 每次处理的记录数量限制 |
| `--skip` | number | 0 | 跳过的记录数量，用于分批处理 |

### 使用示例

```bash
# 1. 翻译中文简体（推荐首次使用）
./start.sh -n auto_translate_i18n -cmd "lib/batchBase.coffee batch/i18n/auto_translate_i18n.coffee --dryrun --lang zh-cn --limit 10"

# 2. 翻译多种语言
./start.sh -n auto_translate_i18n -cmd "lib/batchBase.coffee batch/i18n/auto_translate_i18n.coffee --lang zh-cn,kr --limit 50"

# 3. 强制重新翻译所有韩文
./start.sh -n auto_translate_i18n -cmd "lib/batchBase.coffee batch/i18n/auto_translate_i18n.coffee --lang kr --force-kr-all --limit 20"

# 4. 分批处理大量数据
./start.sh -n auto_translate_i18n -cmd "lib/batchBase.coffee batch/i18n/auto_translate_i18n.coffee --lang zh-cn --limit 100 --skip 0"
./start.sh -n auto_translate_i18n -cmd "lib/batchBase.coffee batch/i18n/auto_translate_i18n.coffee --lang zh-cn --limit 100 --skip 100"
```

## 翻译策略

### 中文翻译
- **优先级**：优先翻译zh-cn（中文简体）
- **自动转换**：如果已有zh-cn，自动生成zh（中文繁体）
- **反向转换**：如果已有zh，自动生成zh-cn
- **LLM翻译**：如果两种中文都不存在，使用LLM翻译

### 韩文翻译
- **提示词获取**：从prompts表获取韩文翻译专用模板
- **LLM翻译**：使用配置的翻译服务进行翻译
- **质量验证**：检查翻译结果是否包含韩文字符

## 数据库操作

### 查询优化
- 使用适当的索引优化查询性能
- 支持src字段过滤（排除RM来源数据）
- 分页查询避免内存溢出

### 更新策略
- 批量更新提高效率
- 保持数据一致性
- 错误重试机制

## 错误处理

### 重试机制
- 翻译失败自动重试（最多3次）
- 指数退避延迟策略
- 网络超时和API限制处理

### 质量验证
- 翻译结果长度检查
- 语言字符验证
- 错误标识检测

## 监控和报告

### 实时监控
- 处理进度百分比
- 处理速度统计
- 成功/失败计数

### 处理报告
- 详细统计信息
- 语言分布统计
- 质量问题汇总
- 性能建议

## 注意事项

1. **首次使用**：建议先使用`--dryrun`模式预览
2. **API限制**：注意翻译服务的API调用限制
3. **数据备份**：重要数据建议先备份
4. **批量大小**：根据系统性能调整`--limit`参数
5. **网络稳定**：确保网络连接稳定，避免翻译中断

## 故障排除

### 常见问题
1. **翻译失败率高**：检查网络连接和API配置
2. **处理速度慢**：调整批量大小和并发数
3. **内存不足**：减少limit参数值
4. **数据库连接超时**：检查数据库连接配置

### 日志分析
- 查看详细错误日志
- 分析翻译质量问题
- 监控处理性能指标

## 维护建议

1. **定期运行**：建议定期运行以处理新增的未翻译内容
2. **质量检查**：定期抽查翻译质量
3. **性能监控**：监控处理性能和资源使用
4. **配置更新**：及时更新翻译服务配置和提示词模板
