should = require('should')
sinon = require('sinon')
helpers = require('../00_common/helpers')
# ./test.sh -f lib/translator.js
{createTranslatorManager,findLanguageValue} = require '../../lib/translator/translatorManager'

describe 'TranslatorManager test',->
  before (done) ->
    @timeout(30000)
    config = helpers.CONFIG(['azure','deepseek','deepL','openAI','gemini','claude','grok','rm'])
    console.log 'TranslatorManager config:', config

    # 创建测试用的配置，确保所有翻译器都有基本配置
    testConfig = {
      azure: {
        subscriptionKey: config.azure?.subscriptionKey or 'test-azure-key'
        endpoint: config.azure?.endpoint or 'https://api.cognitive.microsofttranslator.com'
        region: config.azure?.region or 'eastus'
        maxUsage: 10
      }
      deepL: {
        key: config.deepL?.key or 'test-deepl-key'
        endpoint: config.deepL?.endpoint or 'https://api.deepl.com/v2/translate'
        maxUsage: 10
      }
      deepseek: {
        key: config.deepseek?.key or 'test-deepseek-key'
        endpoint: config.deepseek?.endpoint or 'https://api.deepseek.com/chat/completions'
        maxUsage: 10
      }
      openAI: {
        key: config.openAI?.key or 'test-openai-key'
        endpoint: config.openAI?.endpoint or 'https://api.openai.com/v1/chat/completions'
        orgID: config.openAI?.orgID or 'test-org-id'
        projectID: config.openAI?.projectID or 'test-project-id'
        maxUsage: 10
      }
      gemini: {
        key: config.gemini?.key or 'test-gemini-key'
        maxUsage: 10
      }
      claude: {
        key: config.claude?.key or 'test-claude-key'
        endpoint: config.claude?.endpoint or 'https://api.anthropic.com/v1/messages'
        maxUsage: 10
      }
      grok: {
        key: config.grok?.key or 'test-grok-key'
        endpoint: config.grok?.endpoint or 'https://api.x.ai/v1/chat/completions'
        maxUsage: 10
      }
      rm: {
        endpoint: config.rm?.endpoint or 'http://localhost:11434/api/generate'
        maxUsage: 10
      }
    }

    @translatorManager = createTranslatorManager(testConfig)
    console.log 'Created translators:', Object.keys(@translatorManager.translators)
    done()

  describe 'TranslatorManager constructor test', ->
    it 'should create translator instances for each service', (done) ->
      # 验证翻译器实例已创建（注意deepl的小写）
      @translatorManager.translators.should.have.properties(['azure', 'deepl', 'deepseek', 'openAI', 'gemini', 'claude', 'grok', 'rm'])

      # 验证每个翻译器都有必要的方法
      for serviceName, translator of @translatorManager.translators
        translator.should.have.property('translate')
        translator.should.have.property('isAvailable')
        if serviceName in ['deepseek', 'openAI', 'gemini', 'claude', 'grok', 'rm']
          translator.should.have.property('translateWithCustomPrompt')

      done()

  describe 'TranslatorManager translate test', ->
    tests = [
      {
        desc: 'should attempt translation with the first available service'
        setup: ->
          # 确保翻译器存在后再进行stub
          if @translatorManager.translators.gemini?
            @mockTranslate = sinon.stub(@translatorManager.translators.gemini, 'translate')
              .resolves('Translated text')
          else
            throw new Error('Gemini translator not found')
        input:
          message: 'Hello world'
          translatorList: ['gemini']
          fromLang: 'en'
          toLang: 'zh-cn'
        assertion: (result) ->
          should.deepEqual result, ['Translated text','gemini']
          @mockTranslate.calledOnce.should.be.true()
          @mockTranslate.firstCall.args[0].should.equal('Hello world')
          @mockTranslate.firstCall.args[1].should.equal('English')
          @mockTranslate.firstCall.args[2].should.equal('Chinese')
        teardown: ->
          @mockTranslate?.restore()  # Restore the stub to its original function
      },
      {
        desc: 'should fall back to the next service if the first one fails'
        setup: ->
          # 确保翻译器存在后再进行stub
          if @translatorManager.translators.gemini? and @translatorManager.translators.openAI?
            @failingTranslate = sinon.stub(@translatorManager.translators.gemini, 'translate')
              .rejects(new Error('Translation failed'))
            @successTranslate = sinon.stub(@translatorManager.translators.openAI, 'translate')
              .resolves('Translated text')
          else
            throw new Error('Required translators not found')
        input:
          message: 'Hello world'
          translatorList: ['gemini', 'openAI']
          fromLang: 'en'
          toLang: 'zh-cn'
        assertion: (result) ->
          should.deepEqual result, ['Translated text','openAI']
          @failingTranslate.calledOnce.should.be.true()
          @successTranslate.calledOnce.should.be.true()
        teardown: ->
          @failingTranslate?.restore()
          @successTranslate?.restore()
      },
      {
        desc: 'should throw an error if all services fail'
        setup: ->
          @stubs = []
          # 为所有翻译器设置失败的stub
          for serviceName, translator of @translatorManager.translators
            if translator?
              stub = sinon.stub(translator, 'translate').rejects(new Error('Translation failed'))
              @stubs.push(stub)
        input:
          message: 'Hello world'
          translatorList: ['gemini', 'openAI', 'azure', 'deepL', 'claude']
          fromLang: 'en'
          toLang: 'zh-cn'
        assertion: (error) ->
          error.should.be.an.instanceOf(Error)
          error.message.should.equal('Translation failed with all services')
        teardown: ->
          # 恢复所有stub
          for stub in @stubs
            stub?.restore()
          @stubs = []
      }
    ]

    tests.forEach (test) ->
      it test.desc, () ->
        @timeout(30000)
        test.setup.call(this)

        try
          result = await @translatorManager.translate(test.input.message, test.input.translatorList, test.input.fromLang, test.input.toLang)
          test.assertion.call(this, result)
        catch error
          test.assertion.call(this, error)
        finally
          test.teardown?.call(this)


  describe 'findLanguageValue test', ->
    tests = [
      {
        desc: 'should return correct language value for azure'
        input:
          k: 'en'
          service: 'azure'
        output: 'en'
      },
      {
        desc: 'should return correct language value for deepl'
        input:
          k: 'zh-cn'
          service: 'deepl'
        output: 'ZH'
      },
      {
        desc: 'should return correct language value for openAI'
        input:
          k: 'kr'
          service: 'openAI'
        output: 'Korean'
      },
      {
        desc: 'should throw an error for unsupported service'
        input:
          k: 'en'
          service: 'unsupported'
        error: 'Service \'unsupported\' not supported'
      },
      {
        desc: 'should throw an error for unknown language key'
        input:
          k: 'unknown'
          service: 'azure'
        error: 'Key \'unknown\' not found in LANGUAGE_OBJ'
      }
    ]

    tests.forEach (test) ->
      it test.desc, (done) ->
        @timeout(30000)
        if test.error
          (-> findLanguageValue(test.input.k, test.input.service)).should.throw(test.error)
        else
          result = findLanguageValue(test.input.k, test.input.service)
          should.equal(result, test.output)
        done()

  describe 'TranslatorManager Grok, RM, Gemini API test', ->
    tests = [
      # {
      #   desc: 'should translate text using Grok API'
      #   input:
      #     message: "TWO LEGAL LOTS on a CORNER the location with LANE ACCESS with THREE EXPOSURES!"
      #     translatorList: ['grok']
      #     fromLang: 'en'
      #     toLang: 'zh-cn'
      #   minLength: 5
      #   assertion: ([result,engine]) ->
      #     result.should.be.a.String()
      #     result.should.not.be.empty()
      #     result.length.should.be.above tests[0].minLength
      #     result.should.match(/[\u4e00-\u9fff]/)  # check if the result contains Chinese characters
      #     engine.should.equal('grok')
      # },
      {
        desc: 'should translate text using RM API'
        input:
          message: 'TWO LEGAL LOTS on a CORNER the location with LANE ACCESS with THREE EXPOSURES!'
          translatorList: ['rm']
          fromLang: 'en'
          toLang: 'zh-cn'
        minLength: 5
        assertion: ([result,engine]) ->
          result.should.be.a.String()
          result.should.not.be.empty()
          result.length.should.be.above(5)
          result.should.match(/[\u4e00-\u9fff]/)  # check if the result contains Chinese characters
          engine.should.equal('rm')
        setup: ->
          @originalFetch = global.fetch
          global.fetch = sinon.stub()
          global.fetch.resolves({
            ok: true
            json: -> Promise.resolve({
              response: '两个合法地块位于拐角位置，具有车道通道和三面采光！'
            })
          })
        teardown: ->
          global.fetch = @originalFetch
      },
      {
        desc: 'should translate text using Gemini API'
        input:
          message: 'TWO LEGAL LOTS on a CORNER the location with LANE ACCESS with THREE EXPOSURES!'
          translatorList: ['gemini']
          fromLang: 'en'
          toLang: 'zh-cn'
        minLength: 5
        setup: ->
          if @translatorManager.translators.gemini?
            @mockGeminiTranslate = sinon.stub(@translatorManager.translators.gemini, 'translate')
              .resolves('两个合法地块位于拐角位置，具有车道通道和三面采光！')
        assertion: ([result,engine]) ->
          result.should.be.a.String()
          result.should.not.be.empty()
          result.length.should.be.above(5)
          result.should.match(/[\u4e00-\u9fff]/)  # check if the result contains Chinese characters
          engine.should.equal('gemini')
        teardown: ->
          @mockGeminiTranslate?.restore()
      },
      {
        desc: 'should throw an error if Grok API key is missing'
        input:
          message: 'Hello world'
          translatorList: ['grok']
          fromLang: 'en'
          toLang: 'zh-cn'
        setup: ->
          # 设置无效的API key来模拟认证失败
          if @translatorManager.translators.grok?
            @translatorManager.translators.grok.key = "invalid-key"
        assertion: (error) ->
          error.should.be.an.instanceOf(Error)
          error.message.should.containEql('Translation failed with all services')
      },
      {
        desc: 'should throw an error if Gemini service is down'
        input:
          message: 'Hello world'
          translatorList: ['gemini']
          fromLang: 'en'
          toLang: 'zh-cn'
        setup: ->
          if @translatorManager.translators.gemini?
            @geminiStub = sinon.stub(@translatorManager.translators.gemini, 'translate')
              .rejects(new Error('Service unavailable'))
          else
            throw new Error('Gemini translator not found')
        assertion: (error) ->
          error.should.be.an.instanceOf(Error)
          error.message.should.containEql('Translation failed with all services')
        teardown: ->
          @geminiStub?.restore()
      },
      {
        desc: 'should throw an error if RM service is down'
        input:
          message: 'Hello world'
          translatorList: ['rm']
          fromLang: 'en'
          toLang: 'zh-cn'
        setup: ->
          @originalFetch = global.fetch
          global.fetch = sinon.stub()
          global.fetch.rejects(new Error('Service unavailable'))
        assertion: (error) ->
          error.should.be.an.instanceOf(Error)
          error.message.should.containEql('Translation failed with all services')
        teardown: ->
          global.fetch = @originalFetch
      }
    ]

    tests.forEach (test) ->
      it test.desc, ->
        @timeout(30000)
        if test.setup?
          test.setup.call(this)

        translatePromise = @translatorManager.translate(test.input.message, test.input.translatorList, test.input.fromLang, test.input.toLang)

        return translatePromise
          .then (result) =>
            test.assertion.call(this, result)
          .catch (error) =>
            test.assertion.call(this, error)
          .finally () =>
            if test.teardown?
              test.teardown.call(this)

  describe 'TranslatorManager availability', ->
    beforeEach ->
      # 确保翻译器存在后再设置属性
      if @translatorManager.translators.rm?
        @translatorManager.translators.rm.maxUsage = 10
        @translatorManager.translators.rm.usageCount = 0
      if @translatorManager.translators.azure?
        @translatorManager.translators.azure.maxUsage = 10
        @translatorManager.translators.azure.usageCount = 0

    it 'should return available translator', (done) ->
      # 确保rm翻译器可用
      if @translatorManager.translators.rm?
        @translatorManager.translators.rm.usageCount = 0
        service = @translatorManager.getAvailableTranslator(['rm', 'azure'])
        service.should.equal('rm')
      done()

    it 'should return null when no translator is available', (done) ->
      if @translatorManager.translators.rm?
        # 设置rm翻译器不可用
        @translatorManager.translators.rm.usageCount = @translatorManager.translators.rm.maxUsage
        service = @translatorManager.getAvailableTranslator(['rm'])
        should.not.exist(service)
      done()

    it 'should respect translator order in list', (done) ->
      if @translatorManager.translators.azure? and @translatorManager.translators.rm?
        # 确保azure翻译器可用且在列表前面
        @translatorManager.translators.azure.usageCount = 0
        @translatorManager.translators.rm.usageCount = 0
        service = @translatorManager.getAvailableTranslator(['azure', 'rm'])
        service.should.equal('azure')
      done()

  describe 'TranslatorManager translateWithPrompt test', ->
    tests = [
      {
        desc: 'should translate with custom prompt successfully'
        input:
          content: 'Save changes'
          prompt: 'Please translate the following UI text to Chinese: '
          targetLang: 'zh-cn'
          options: {}
        setup: ->
          if @translatorManager.translators.gemini?
            @mockTranslate = sinon.stub(@translatorManager.translators.gemini, 'translateWithCustomPrompt')
              .resolves('保存更改')
        assertion: (result) ->
          result.should.have.property('success', true)
          result.should.have.property('result', '保存更改')
          result.should.have.property('service', 'gemini')
          @mockTranslate.calledOnce.should.be.true()
        teardown: ->
          @mockTranslate?.restore()
      },
      {
        desc: 'should skip i18n filter when skipI18nFilter is true'
        input:
          content: 'This property has excellent location and amenities.'
          prompt: 'Please translate the following property description to Chinese: '
          targetLang: 'zh-cn'
          options: { skipI18nFilter: true }
        setup: ->
          if @translatorManager.translators.gemini?
            @mockTranslate = sinon.stub(@translatorManager.translators.gemini, 'translateWithCustomPrompt')
              .resolves('这个房产位置优越，设施齐全。')
        assertion: (result) ->
          result.should.have.property('success', true)
          result.should.have.property('result', '这个房产位置优越，设施齐全。')
          result.should.have.property('skipI18nFilter', true)
        teardown: ->
          @mockTranslate?.restore()
      },
      {
        desc: 'should return error for invalid content parameter'
        input:
          content: null
          prompt: 'Please translate: '
          targetLang: 'zh-cn'
          options: {}
        assertion: (result) ->
          result.should.have.property('success', false)
          result.should.have.property('error', 'Invalid content parameter')
      },
      {
        desc: 'should return error for invalid prompt parameter'
        input:
          content: 'Hello world'
          prompt: null
          targetLang: 'zh-cn'
          options: {}
        assertion: (result) ->
          result.should.have.property('success', false)
          result.should.have.property('error', 'Invalid prompt parameter')
      }
    ]

    tests.forEach (test) ->
      it test.desc, ->
        @timeout(30000)
        if test.setup?
          test.setup.call(this)

        # translateWithPrompt 对于无效参数会同步返回错误对象，对于有效参数会返回Promise
        result = @translatorManager.translateWithPrompt(
          test.input.content,
          test.input.prompt,
          test.input.targetLang,
          test.input.options
        )

        # 检查是否是同步返回的错误对象
        if result and typeof result is 'object' and result.success is false
          # 同步错误情况
          test.assertion.call(this, result)
          if test.teardown?
            test.teardown.call(this)
          return Promise.resolve()
        else
          # 异步情况，返回Promise
          return result
            .then (asyncResult) =>
              test.assertion.call(this, asyncResult)
            .catch (error) =>
              test.assertion.call(this, error)
            .finally () =>
              if test.teardown?
                test.teardown.call(this)

  describe 'TranslatorManager filterComments test', ->
    tests = [
      {
        desc: 'should pass appropriate content'
        input:
          content: 'This is a nice property with great location.'
          filterPrompt: 'Please check if this content is appropriate. Reply PASS or REJECT: '
          options: {}
        setup: ->
          if @translatorManager.translators.gemini?
            @mockFilter = sinon.stub(@translatorManager.translators.gemini, 'translateWithCustomPrompt')
              .resolves('PASS: Content is appropriate')
        assertion: (result) ->
          result.should.have.property('success', true)
          result.should.have.property('passed', true)
          result.should.have.property('reason', 'Content approved')
          result.should.have.property('service', 'gemini')
        teardown: ->
          @mockFilter?.restore()
      },
      {
        desc: 'should reject inappropriate content'
        input:
          content: 'This content contains inappropriate language.'
          filterPrompt: 'Please check if this content is appropriate. Reply PASS or REJECT: '
          options: {}
        setup: ->
          if @translatorManager.translators.gemini?
            @mockFilter = sinon.stub(@translatorManager.translators.gemini, 'translateWithCustomPrompt')
              .resolves('REJECT: Contains inappropriate language')
        assertion: (result) ->
          result.should.have.property('success', true)
          result.should.have.property('passed', false)
          result.should.have.property('reason', 'Contains inappropriate language')
        teardown: ->
          @mockFilter?.restore()
      },
      {
        desc: 'should return error for invalid content parameter'
        input:
          content: null
          filterPrompt: 'Please check: '
          options: {}
        assertion: (result) ->
          result.should.have.property('success', false)
          result.should.have.property('error', 'Invalid content parameter')
      },
      {
        desc: 'should return error for invalid filterPrompt parameter'
        input:
          content: 'Hello world'
          filterPrompt: null
          options: {}
        assertion: (result) ->
          result.should.have.property('success', false)
          result.should.have.property('error', 'Invalid filterPrompt parameter')
      },
      {
        desc: 'should reject empty content'
        input:
          content: '   '
          filterPrompt: 'Please check: '
          options: {}
        assertion: (result) ->
          result.should.have.property('success', true)
          result.should.have.property('passed', false)
          result.should.have.property('reason', 'Empty content')
      }
    ]

    tests.forEach (test) ->
      it test.desc, ->
        @timeout(30000)
        if test.setup?
          test.setup.call(this)

        # filterComments 对于无效参数会同步返回错误对象，对于有效参数会返回Promise
        result = @translatorManager.filterComments(
          test.input.content,
          test.input.filterPrompt,
          test.input.options
        )

        # 检查是否是同步返回的错误对象
        if result and typeof result is 'object' and result.success is false
          # 同步错误情况
          test.assertion.call(this, result)
          if test.teardown?
            test.teardown.call(this)
          return Promise.resolve()
        else
          # 异步情况，返回Promise
          return result
            .then (asyncResult) =>
              test.assertion.call(this, asyncResult)
            .catch (error) =>
              test.assertion.call(this, error)
            .finally () =>
              if test.teardown?
                test.teardown.call(this)
